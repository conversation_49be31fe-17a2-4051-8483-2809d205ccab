package ui.layout.left.display.components.tappane.case_mgmt.excelcase;

import com.alibaba.fastjson2.JSONObject;
import common.constant.AppConstants;
import common.utils.DateUtils;
import common.utils.FileUtils;
import common.utils.NetworkUtils;
import common.utils.StringUtils;
import excelcase.AutoScrollPane;
import excelcase.config.json.CaseConfigJson;
import excelcase.config.json.CaseConfigJsonManager;
import excelcase.exportcase.CaseReportFileConfig;
import lombok.Getter;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import sdk.base.JsonResponse;
import sdk.base.SseSession;
import sdk.base.execution.ExcelCaseExecutionNotification;
import sdk.base.operation.TestCaseExecutionInfo;
import sdk.base.operation.TestCaseInfo;
import sdk.base.operation.TestSuiteInfo;
import sdk.constants.PolyTestConstants;
import sdk.constants.UrlConstants;
import sdk.domain.SmokingTestConfigModel;
import sdk.domain.action_sequence.ActionSequence;
import sdk.domain.action_sequence.ActionSequenceCheckReporter;
import sdk.domain.action_sequence.ActionSequenceContext;
import sdk.domain.action_sequence.ActionSequenceStatus;
import sdk.entity.OperationTargetHolder;
import ui.base.cosntants.ColumnNameConstants;
import ui.base.cosntants.ExcelConstants;
import ui.callback.ExcelCaseExecutionMonitorListener;
import ui.callback.InsertTestCaseListener;
import ui.config.json.SmokingTestJsonConfig;
import ui.entry.ClientView;
import ui.layout.left.display.components.tappane.case_mgmt.excelcase.context.ExcelCaseContext;
import ui.layout.left.display.components.tappane.case_mgmt.excelcase.context.SingleCaseContext;
import ui.layout.right.components.log.LogCmd;
import ui.layout.right.components.log.LogMessage;
import ui.layout.right.components.testcase.TestStepTable;
import ui.model.MainModel;
import ui.model.mail.TestResult;
import ui.model.mail.TestResultReportDto;
import ui.utils.SwingUtil;

import javax.swing.*;
import javax.swing.Timer;
import javax.swing.table.TableModel;
import java.awt.*;
import java.io.File;
import java.io.IOException;
import java.io.OutputStream;
import java.lang.reflect.InvocationTargetException;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Stream;

import static common.utils.StringUtils.calculateFailureRate;
import static sdk.constants.ExportTypeConstants.EXPORT_REPORT_FILE;
import static sdk.constants.PolyTestConstants.getValueByStatus;
import static ui.base.cosntants.ExcelConstants.Execution_TIMES;
import static ui.base.cosntants.ExcelConstants.FAIL_RATE;

/**
 * Excel测试用例执行引擎
 * 负责:
 * 1. 测试用例的执行调度和控制
 * 2. 测试结果收集和显示
 * 3. 测试报告生成
 * 4. 测试状态监控
 */
@Slf4j
public class ExcelCaseTestEngine implements ExcelCaseExecutionMonitorListener {
    private SwingWorker<Void, ExcelCaseTable.RowGroup> executeTestSwingWorker;
    private SwingWorker<Void, Void> singleExecuteTestSwingWorker;
    private final ExcelCaseControlPanel excelCaseControlPanel;
    private final ExcelCaseRenderTabbedPane excelCaseRenderTabbedPane;
    private final ClientView clientView;
    private final MainModel mainModel;
    private boolean firstExecuteTest = true;
    private final AtomicBoolean shouldPause = new AtomicBoolean(false);
    private final AtomicBoolean shouldStop = new AtomicBoolean(true);
    @Getter
    private boolean singleTest = false;
    private final Object pauseLock;
    private final SseSession sseSession;
    private final SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd/ HH:mm:ss");
    @Getter
    private final SimpleDateFormat reportSimpleDateFormat = new SimpleDateFormat("yyyyMMdd_HH-mm-ss");

    private final ExcelCaseTestListener excelCaseTestListener;
    private final static String EXCEL_CASE_TEST_STATUS_SUBSCRIBE_ID = "excelCaseTestStatus";
    private final static String CHECK_RESULT_SUBSCRIBE_ID = "checkActualResult";
    private final static String TEST_STEP_EXECUTE_SUBSCRIBE_ID = "testStepExecute";
    private final static String TEST_STEP_EXECUTE_RESULT_SUBSCRIBE_ID = "testStepExecuteResult";
    public static final String UDS_LOG_PATH_SUBSCRIBE_ID = "udsLogPathNotification";


    private final ExecutorService executorService;
    private JDialog dialog;
    private String startTime;
    private static final Map<String, String> DRIVE_TO_UNC_MAP = new HashMap<>();
    private final Map<String, TestResult> testResultMap = new ConcurrentHashMap<>();

    static {
        DRIVE_TO_UNC_MAP.put("K:", "\\\\hzhe003a\\DFS\\DIDA3999");
        DRIVE_TO_UNC_MAP.put("M:", "\\\\hzhe003a\\DFS\\DIDA3019");
        DRIVE_TO_UNC_MAP.put("N:", "\\\\hzhe003a\\DFS\\DIDA3020");
        DRIVE_TO_UNC_MAP.put("P:", "\\\\hzhe003a\\DFS\\DIDA3003");
        DRIVE_TO_UNC_MAP.put("R:", "\\\\hzhe003a\\DFS\\DIDA3096");
        DRIVE_TO_UNC_MAP.put("W:", "\\\\hzhe003a\\DFS\\DIDA3098");
    }

    /**
     * 构造测试引擎，初始化相关组件。
     *
     * @param excelCaseControlPanel     控制面板
     * @param excelCaseRenderTabbedPane 用例渲染面板
     * @param clientView                客户端视图
     * @param mainModel                 主模型
     * @param excelCaseTestListener     用例测试监听器
     */
    public ExcelCaseTestEngine(ExcelCaseControlPanel excelCaseControlPanel,
                               ExcelCaseRenderTabbedPane excelCaseRenderTabbedPane,
                               ClientView clientView, MainModel mainModel,
                               ExcelCaseTestListener excelCaseTestListener) {
        this.excelCaseTestListener = excelCaseTestListener;
        this.excelCaseControlPanel = excelCaseControlPanel;
        this.excelCaseRenderTabbedPane = excelCaseRenderTabbedPane;
        this.clientView = clientView;
        this.mainModel = mainModel;
        pauseLock = new Object();
        sseSession = new SseSession();
        executorService = Executors.newFixedThreadPool(5, r -> {
            Thread thread = new Thread(r);
            thread.setName(String.format("ExcelCaseTestEngineThread-%d", thread.getId()));
            return thread;
        });
    }

    /**
     * 启动测试引擎，包括启动测试线程、启动监控线程等。
     */
    private volatile boolean sseMonitorsRegistered = false;

    private void startMonitorTestThread() {
        executeFailMonitor();
        executeResultColorMonitor();
        testLogPathMonitor();
        testingRowColorMonitor();
        checkActualResultMonitor();

    }

    /**
     * 监听后端获取CAPL Log，通知前端暂停。
     */
    private void testLogPathMonitor() {
        String path = UrlConstants.getSseUrl(UDS_LOG_PATH_SUBSCRIBE_ID);
        if (!sseSession.getReaders().contains(path)) {
            executorService.submit(() -> {
                try {
                    //左边测试用例表的check的实际结果写入
                    sseSession.readStream(path,
                            line -> {
                                log.info("------------接收到后端日志路径--------:{}", line);
                                String jsonContent = line.substring(line.indexOf(":") + 1).replace("\"", "");
                                updateTestLog(jsonContent);
                            });
                } catch (Exception e) {
                    log.error("SSE:{}->{}", UDS_LOG_PATH_SUBSCRIBE_ID, e.getMessage());
                }
            });
        }
    }


    /**
     * 监听后端执行失败，通知前端暂停。
     */
    private void executeFailMonitor() {
        //判断sse是否存在
        //如果不存在，则创建
        String path = UrlConstants.getSseUrl(EXCEL_CASE_TEST_STATUS_SUBSCRIBE_ID);
        if (!sseSession.getReaders().contains(path)) {
            Future<?> failMonitorFuture = executorService.submit(() -> {
                try {
                    //发生异常时，通知前端暂停
                    sseSession.readStream(path,
                            line -> {
                                log.info("------------接收到后端异常--------:{}", line);
                                if (excelCaseControlPanel.isPressureMode() && mainModel.getAppInfo().isPauseWhenTestFailed()) {
                                    log.info("接收到后端暂停状态");
                                    excelCaseControlPanel.updatePausedUiState();
                                }
                            });
                } catch (Exception e) {
                    log.error("SSE通知:{}->{}", EXCEL_CASE_TEST_STATUS_SUBSCRIBE_ID, e.getMessage());
                }
            });
        }
    }

    /**
     * 监听后端执行结果，更新测试用例表和测试小窗口的行背景色渲染。
     */
    private void executeResultColorMonitor() {
        String path = UrlConstants.getSseUrl(TEST_STEP_EXECUTE_RESULT_SUBSCRIBE_ID);
        if (!sseSession.getReaders().contains(path)) {
            executorService.submit(() -> {
                try {
                    sseSession.readStream(path,
                            line -> {
                                String jsonContent = line.substring(line.indexOf("{"));
                                ActionSequence actionSequence = JSONObject.parseObject(jsonContent, ActionSequence.class);
                                mainModel.getTestStepModel().renderTestStepExecuteResultColor(actionSequence);
                            });
                } catch (Exception e) {
                    log.error("执行结果监听器:{}，详细日志:", e.getMessage(), e);
                }
            });
        }
    }

    /**
     * 监听后端执行结果，更新测试用例表和测试小窗口的行背景色渲染。
     */
    private void checkActualResultMonitor() {
        String path = UrlConstants.getSseUrl(CHECK_RESULT_SUBSCRIBE_ID);
        if (!sseSession.getReaders().contains(path)) {
            executorService.submit(() -> {
                try {
                    //左边测试用例表的check的实际结果写入
                    sseSession.readStream(path,
                            line -> {
                                String jsonContent = line.substring(line.lastIndexOf(":") + 1).replace("\"", "");
                                updateActualResult(jsonContent);
                            });
                } catch (Exception e) {
                    log.error("SSE:{}->{}", CHECK_RESULT_SUBSCRIBE_ID, e.getMessage());
                }
            });
        }
    }

    /**
     * 测试小窗口正在执行中的行背景色渲染
     */
    private void testingRowColorMonitor() {
        String path = UrlConstants.getSseUrl(TEST_STEP_EXECUTE_SUBSCRIBE_ID);
        if (!sseSession.getReaders().contains(path)) {
            executorService.submit(() -> {
                try {
                    //测试小窗口正在执行中的行背景色渲染
                    sseSession.readStream(path,
                            line -> {
                                String testStepUuid = line.substring(line.lastIndexOf(":") + 1).replace("\"", "");
                                mainModel.getTestStepModel().renderTestStepRowColorByUuid(testStepUuid);
                            });
                } catch (Exception e) {
                    log.error("SSE:{}->{}", TEST_STEP_EXECUTE_SUBSCRIBE_ID, e.getMessage());
                }
            });
        }
    }


    /**
     * 执行升级操作并根据状态处理界面逻辑。
     *
     * @param msg 升级信息
     */
    public void upgradeOperation(String msg) {
//       System.out.println("upgradeOperation msg----" + msg);
        if (msg.equals("notificationUpgrade")) {
            //还在测试直接终止
            if (!shouldStop.get()) {
                excelCaseControlPanel.stopTestCase();
            }
            showDialog();
            OperationTargetHolder.getSmokingTestKit().startUpgrade();
//        } else if(msg.equals("startUpgrade")) {
//            //界面提示用户正在升级，不能操作界面
//            showDialog();
        } else {
            closeDialog();
            //升级结束
            //从第一张表开始进行冒烟测试
            if (excelCaseRenderTabbedPane.getTabCount() > 0) {
                excelCaseRenderTabbedPane.setSelectedIndex(0);
                excelCaseControlPanel.startTestCase(true);
            }
        }
    }

    /**
     * 关闭升级提示对话框。
     */
    private void closeDialog() {
        if (dialog != null && dialog.isVisible()) {
            dialog.dispose();
        }
    }

    /**
     * 显示升级提示对话框。
     */
    private void showDialog() {
//        JOptionPane.showMessageDialog(clientView, "软件正在升级中...!");
        dialog = new JDialog(clientView, "升级提示", false);
        dialog.setSize(500, 300);
        dialog.setLocationRelativeTo(clientView);
        Container contentPane = dialog.getContentPane();
        contentPane.setLayout(new BorderLayout());

        JLabel label = new JLabel("软件正在升级中...");
        JButton cancelButton = new JButton("取消");
        cancelButton.addActionListener(cancelEvent -> {
            dialog.dispose();
            stopTest();
        });
        contentPane.add(label, BorderLayout.CENTER);
        contentPane.add(cancelButton, BorderLayout.SOUTH);
//        dialog.setLayout(new BoxLayout(dialog.getContentPane(), BoxLayout.Y_AXIS));
        dialog.setVisible(true);
    }

    /**
     * 判断是否处于暂停状态。
     *
     * @return 当前是否暂停
     */
    public boolean isPause() {
        return shouldPause.get();
    }

    /**
     * 更新界面至暂停状态。
     */
    public void updatePausedUiState() {
        shouldPause.set(true);
        excelCaseTestListener.pauseTest();
    }

    /**
     * 手动执行暂停测试的操作。
     */
    public void manuallyPauseTest() {
        updatePausedUiState();
        OperationTargetHolder.getActionSequenceKit().pauseActionSequence();
    }

//    public void pauseByTestFailed() {
//        ClientManager.getActionSequenceKit().failActionSequence();
//        shouldPause = true;
//        excelCaseTestListener.pauseTest();
//    }

    /**
     * 恢复暂停的测试。
     */
    public void resumeTest() {
        log.info("恢复Excel测试");
        shouldPause.set(false);
        OperationTargetHolder.getActionSequenceKit().resumeActionSequence();
        synchronized (pauseLock) {
            pauseLock.notify();
        }
        excelCaseTestListener.resumeTest();
    }

    /**
     * 停止单步调试模式。
     */
    public void stopSingleTest() {
        singleTest = false;
        OperationTargetHolder.getActionSequenceKit().stopSingleActionSequence();
        log.info("停止Excel单步调试");
        if (singleExecuteTestSwingWorker != null && !singleExecuteTestSwingWorker.isCancelled()) {
            singleExecuteTestSwingWorker.cancel(false);
        }
    }

    /**
     * 停止所有测试进程。
     */
    public void stopTest() {
        log.info("停止Excel测试");
        OperationTargetHolder.getActionSequenceKit().stopActionSequence();
        if (executeTestSwingWorker != null && !executeTestSwingWorker.isCancelled()) {
            executeTestSwingWorker.cancel(false);
        }
        shouldPause.set(false);
        shouldStop.set(true);
        synchronized (pauseLock) {
            pauseLock.notify();
        }
        firstExecuteTest = true;
        sseMonitorsRegistered = false; // 重置
//        uploadExcelTestCaseExecuteInfo(testingRow, new ActionSequenceCheckReporter());
    }


    /**
     * 更新实际结果。
     *
     * @param jsonContent 实际结果
     */
    private void updateActualResult(String jsonContent) {
        ExcelCaseTable table = excelCaseRenderTabbedPane.getSelectedExcelCaseTable();
        int columnIndex = SwingUtil.getColumnIndex(table, ColumnNameConstants.getInstance().getActualResult());
        if (columnIndex == -1) return;
        //TODO：需要考虑到测试过程中，测试员选中其他行，结果会写到其他行，而不是执行测试行
        int testingRow = table.getSelectedRow();
        String valueAt = (String) table.getModel().getValueAt(testingRow, columnIndex);
        table.setValueAt((valueAt == null ? "" : valueAt) + "\n" + jsonContent, testingRow, columnIndex);
        table.repaint();
    }

    /**
     * 更新Test Log列的内容。
     *
     * @param jsonContent 实际结果
     */
    private void updateTestLog(String jsonContent) {
        ExcelCaseTable table = excelCaseRenderTabbedPane.getSelectedExcelCaseTable();
        int columnIndex = SwingUtil.getColumnIndex(table, ColumnNameConstants.getInstance().getTestLog());
        if (columnIndex == -1) {
            return;
        }
        int testingRow = table.getSelectedRow();
        table.setValueAt(jsonContent, testingRow, columnIndex);
        table.repaint();
    }

    /**
     * 开始单步调试指定的用例。
     *
     * @param excelCaseContext 单步调试上下文
     */
    public void startSingleCase(SingleCaseContext excelCaseContext) {
//        SwingUtil.invokeLater(excelCaseTestListener::startTest);
        if (!excelCaseControlPanel.getStartTestCaseButton().isEnabled() && excelCaseControlPanel.getPauseTestingCaseButton().getText().equals("暂停测试")) {
            JOptionPane.showMessageDialog(excelCaseRenderTabbedPane, "测试中启用单步调试，需要手动暂停测试，才能使用单步调试");
            return;
        }
        if (singleTest) {
            JOptionPane.showMessageDialog(excelCaseRenderTabbedPane, "已经正在执行单步调试，不支持多次启用！");
            return;
        }
        excelCaseControlPanel.getExcelCaseTabPaneView().getStopSingleTestButton().setVisible(true);
        singleExecuteTestSwingWorker = new SwingWorker<Void, Void>() {
            final ExcelCaseTable excelCaseTable = excelCaseContext.getExcelCaseTable();

            @Override
            protected Void doInBackground() throws Exception {
                singleTest = true;
                startMonitorTestThread();
                ActionSequenceContext actionSequenceContext = excelCaseContext.getActionSequenceContext();
                actionSequenceContext.setSingleCase(true);
                actionSequenceContext.setTestConfig(excelCaseContext.getTestConfig());
                int testNoColumnId = excelCaseTable.findTableColumnIndexByName(ColumnNameConstants.getInstance().getNo());
                int testCaseIDColumnId = excelCaseTable.findTableColumnIndexByName(ColumnNameConstants.getInstance().getTestCaseID());
                int testKeyColumnId = excelCaseTable.findTableColumnIndexByName(ColumnNameConstants.getInstance().getTestKey());
                double testNo = 0;
                if (testNoColumnId != -1) {
                    Object testNoValue = excelCaseTable.getModel().getValueAt(excelCaseContext.getRow(), testNoColumnId);
                    String testNoStr = testNoValue == null ? "" : testNoValue.toString();
                    if (!StringUtils.isEmpty(testNoStr)) {
                        testNo = Double.parseDouble(testNoStr);
                    }
                }
                String tcID = "";
                if (testCaseIDColumnId != -1) {
                    Object tcIdValue = excelCaseTable.getModel().getValueAt(excelCaseContext.getRow(), testCaseIDColumnId);
                    tcID = tcIdValue == null ? "" : tcIdValue.toString();
                }
                String testKey = "";
                if (testKeyColumnId != -1) {
                    Object testKeyValue = excelCaseTable.getModel().getValueAt(excelCaseContext.getRow(), testKeyColumnId);
                    testKey = testKeyValue == null ? "" : testKeyValue.toString();
                }
                actionSequenceContext.setTestNo(testNo);
                actionSequenceContext.setTcId(tcID);
                actionSequenceContext.setTestKey(testKey);
                actionSequenceContext.setTableName(excelCaseTable.getSheetName());
                actionSequenceContext.setProjectName(mainModel.getAppInfo().getProject());
                actionSequenceContext.setCancelSingleTestCheckAll(CaseConfigJson.getInstance().isSelectedCancelCheckAll());
                actionSequenceContext.setCancelSingleTestCheckAny(CaseConfigJson.getInstance().isSelectedCancelAnyCheck());
                ActionSequenceCheckReporter actionSequenceCheckReporter = excelCaseTable.executeSingleActionSequence(
                        excelCaseContext.getRow(),
                        excelCaseContext.getColumn(),
                        excelCaseContext.getUserColumnName(),
                        actionSequenceContext);
                //修改单元格的内容
                updateTableResultGUI(excelCaseTable, excelCaseContext.getRow(), actionSequenceCheckReporter);
                excelCaseTable.saveExcelCaseRowDataToDB(excelCaseContext.getRow());
//                ClientManager.getActionSequenceManager().closeAllSSE();
                return null;
            }

            @Override
            protected void done() {
                excelCaseTable.clearSelection();
                excelCaseControlPanel.getExcelCaseTabPaneView().getStopSingleTestButton().setVisible(false);
                singleTest = false;
                TestStepTable testStepTable = clientView.getTestStepView().getTestStepTable();
                testStepTable.clearSelection();
            }
        };
        singleExecuteTestSwingWorker.execute();
    }

    /**
     * 停止单步调试。
     *
     * @param excelCaseTableList 用例列表
     * @param testCycles         测试次数
     */
    private void uploadStartTestStatus(List<ExcelCaseTable> excelCaseTableList, int testCycles) {
        log.info("开始上传开始测试状态到云平台执行时长");
        long sTime = System.currentTimeMillis();
        List<TestCaseInfo> testCaseInfoList = new ArrayList<>();
        for (ExcelCaseTable excelCaseTable : excelCaseTableList) {
            int testCaseNameColumnId = excelCaseTable.findTableColumnIndexByName(ColumnNameConstants.getInstance().getTestKey());
            int initialConditionColumnId = excelCaseTable.findTableColumnIndexByName(ColumnNameConstants.getInstance().getInitialCondition());
            int actionColumnId = excelCaseTable.findTableColumnIndexByName(ColumnNameConstants.getInstance().getAction());
            int expectedResultColumnId = excelCaseTable.findTableColumnIndexByName(ColumnNameConstants.getInstance().getExpectedResult());
            List<Integer> excelRowList = excelCaseTable.getCheckedRows();
            int index = 0;
            for (Integer excelRow : excelRowList) {
                TestCaseInfo testCaseInfo = new TestCaseInfo();
                int sumCycle = excelCaseTable.getExcelRowExecuteTimes(excelRow) * testCycles;
                String testCaseName = "";
                String preCondition = "";
                String operationalStep = "";
                String expectationResult = "";
                if (testCaseNameColumnId != -1 && excelCaseTable.getModel().getValueAt(excelRow, testCaseNameColumnId) != null) {
                    testCaseName = excelCaseTable.getModel().getValueAt(excelRow, testCaseNameColumnId).toString();
                }
                if (initialConditionColumnId != -1 && excelCaseTable.getModel().getValueAt(excelRow, initialConditionColumnId) != null) {
                    preCondition = excelCaseTable.getModel().getValueAt(excelRow, initialConditionColumnId).toString();
                }
                if (actionColumnId != -1 && excelCaseTable.getModel().getValueAt(excelRow, actionColumnId) != null) {
                    operationalStep = excelCaseTable.getModel().getValueAt(excelRow, actionColumnId).toString();
                }
                if (expectedResultColumnId != -1 && excelCaseTable.getModel().getValueAt(excelRow, expectedResultColumnId) != null) {
                    expectationResult = excelCaseTable.getModel().getValueAt(excelRow, expectedResultColumnId).toString();
                }
                index = index + 1;
                testCaseInfo.setTestcaseName(testCaseName);
                testCaseInfo.setTestcaseIndex(index);
                testCaseInfo.setSumCycle(sumCycle);
                testCaseInfo.setPreCondition(preCondition);
                testCaseInfo.setOperationalStep(operationalStep);
                testCaseInfo.setExpectationResult(expectationResult);
                testCaseInfoList.add(testCaseInfo);
            }
        }
        String testSuiteName = String.format("%s_%s", mainModel.getAppInfo().getProject(), DateUtils.getNowForFile());
        int caseNum = testCaseInfoList.size();
        TestSuiteInfo testSuiteInfo = TestSuiteInfo.builder()
                .softwareVersion("")
                .hardwareVersion("")
                .testboardVersion("")
                .pcName(NetworkUtils.getComputerInfo().getComputerName())
                .projectId(mainModel.getAppInfo().getProjectId())
                .startTime(DateUtils.getNow())
                .testCases(testCaseInfoList)
                .testType(getValueByStatus(PolyTestConstants.TestTypeConstants.CICD_TEST))
                .testsuiteName(testSuiteName)
                .toolId(AppConstants.PLATFORM_CODE)
                .userId(mainModel.getAppInfo().getUserId())
                .testCaseTotalAll(mainModel.getAppInfo().getCaseSum())
                .checkTotal(mainModel.getAppInfo().getCaseSum())
                .testCaseTotalReview(caseNum)
                .build();
        JsonResponse<String> response = OperationTargetHolder.getTestSuiteKit().startTestSuite(testSuiteInfo);
        if (!mainModel.getAppInfo().isOfflineMode() && (response == null || response.getCode() == -1)) {
//            SwingUtil.showWarningDialog(null, "连接云平台超时,将切换到离线模式");
            SwingUtil.showNonModalDialog(null, "连接云平台超时,将切换到离线模式");
            mainModel.getAppInfo().setOfflineMode(true);
        }
        log.info("上传开始测试状态到云平台执行时长:{}毫秒", System.currentTimeMillis() - sTime);
    }

    /**
     * 按指定模式循环执行多用例测试。
     *
     * @param excelCaseContext 用例上下文
     */
    public void startMultipleCases(ExcelCaseContext excelCaseContext) {
        List<ExcelCaseTable> excelCaseTableList = tableCheckedRows(excelCaseContext);
        if (excelCaseTableList == null) return;
        ActionSequenceStatus actionSequenceStatus = new ActionSequenceStatus();
        actionSequenceStatus.setSimulated(excelCaseContext.isSimulated());
        actionSequenceStatus.setSendCompletedEmailEnabled(false);//Excel测试不是脚本测试
        startTime = DateUtils.getNow();
        SwingUtil.invokeLater(excelCaseTestListener::startTest);
        excelCaseContext.setSmokingTest(mainModel.getAppInfo().isSmokeTest());
        boolean pressureModePauseWhenTestFailed = true;
        boolean isPressureMode = excelCaseContext.isPressureMode();
        int testCycles = excelCaseContext.getSumTestCycles();
        int rowExecuteIntervalTime = excelCaseContext.getRowExecuteIntervalTime();
//        int testCycles = isPressureMode ? sumTestCycles : 1;
        shouldPause.set(false);
        shouldStop.set(false);
        String startTimeFormat = reportSimpleDateFormat.format(new Date());
        if (firstExecuteTest) {
            createTestReport(excelCaseContext, startTimeFormat);
            firstExecuteTest = false;
        }
        createCloudPath(startTimeFormat);
        if (!excelCaseContext.isSimulated()) {
            uploadStartTestStatus(excelCaseTableList, testCycles); //FIXME：耗时
        }
        executeTestSwingWorker = new SwingWorker<Void, ExcelCaseTable.RowGroup>() {
            private ExcelCaseTable excelCaseTable;

            @Override
            protected Void doInBackground() {
                boolean completeOk = true;
                prepareExecution(actionSequenceStatus);
                startMonitorTestThread();
                log.info("开始Excel测试");
                for (int testCycle = 0; testCycle < testCycles; testCycle++) {
                    if (shouldStop.get()) {
                        break;
                    }
                    log.info("~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~");
                    log.info("执行第{}个循环测试", testCycle + 1);
                    LogCmd.getInstance().printLog(LogMessage.info(String.format("----执行第%d个循环测试----", testCycle + 1)));
                    int finalTestCycle = testCycle;
                    for (ExcelCaseTable caseTable : excelCaseTableList) {
                        int executedCases = 0;
                        int pastCases = 0;
                        if (shouldStop.get()) {
                            break;
                        }
                        excelCaseTable = caseTable;
                        excelCaseTable.clearActionSequenceCheckReporter();
                        SwingUtil.invokeLater(() -> excelCaseTable.showProgressBar(finalTestCycle + 1, excelCaseTable.getCheckedRows().size()));
                        if (testCycle == 0) {
                            SwingUtil.invokeLater(() -> clearRowFailResult(excelCaseTable));
                        }
                        SwingUtil.invokeLater(() -> clearTestResult(excelCaseTable));
                        updateTestModeConfig();
                        SwingUtil.invokeLater(() -> {
                            excelCaseRenderTabbedPane.selectTable(excelCaseTable);
                            excelCaseTable.clearColor();
                        });
                        List<ExcelRow> selectedRowsList = excelCaseTable.getSelectedRowsByCheckBox();
                        if (!selectedRowsList.isEmpty()) {
                            SwingUtil.invokeLater(() -> ((AutoScrollPane) excelCaseTable.getScrollPane()).jumpToRow(selectedRowsList.get(0).getRowNumber()));
                        }
                        List<Integer> excelRowList = excelCaseTable.getCheckedRows();
                        // 遍历行数
                        for (Integer excelRow : excelRowList) {
                            //等待TestStep面板加载完成
                            try {
                                SwingUtilities.invokeAndWait(() -> excelCaseTable.createTestStep(excelRow));
                            } catch (InterruptedException | InvocationTargetException e) {
                                log.warn(e.getMessage(), e);
                            }
                            if (shouldStop.get()) {
                                break;
                            }
                            excelCaseTable.setRowSelectionInterval(excelRow, excelRow);
                            excelCaseTable.setTestingRow(excelRow);
                            executedCases++;
                            boolean pass = true;
                            //增加行的执行次数
                            int tableRowExecuteTimes = excelCaseTable.getExcelRowExecuteTimes(excelRow);
                            for (int times = 1; times <= tableRowExecuteTimes; times++) {
                                if (shouldStop.get()) {
                                    break;
                                }
                                int finalTimes = times;
                                SwingUtil.invokeLater(() -> mainModel.getTestCaseTableModel().outputStatusInfo(String.format(" <当前用例表:%s，行:%d，单行压测第%d次>",
                                        excelCaseTable.getSheetName(), excelRow + 1, finalTimes)));
                                log.info("------------------------------------------------");
                                log.info("执行表格:{}，Case行:{}，第{}次", excelCaseTable.getSheetName(), excelRow + 1, times);
                                publish(new ExcelCaseTable.RowGroup(excelRowList.indexOf(excelRow), excelRow));
                                ActionSequenceContext actionSequenceContext = excelCaseTable.combineActionSequenceContext(excelRow);
                                actionSequenceContext.setUuid(excelCaseTable.getRowUuid(excelRow));
                                actionSequenceContext.setPressureMode(isPressureMode);
                                actionSequenceContext.setPauseWhenTestFailed(pressureModePauseWhenTestFailed);
                                actionSequenceContext.setTableName(excelCaseTable.getSheetName());
                                actionSequenceContext.setRow(excelRow);
                                actionSequenceContext.setSimulated(excelCaseContext.isSimulated());
                                actionSequenceContext.setTotalCycleTimes(testCycles);
                                actionSequenceContext.setProjectName(mainModel.getAppInfo().getProject());
                                actionSequenceContext.setCurrentCycleTimes(testCycle + 1);
                                actionSequenceContext.setRowCurrentTestTimes(times);
                                actionSequenceContext.setRowTotalTestTimes(tableRowExecuteTimes);
                                actionSequenceContext.setSingleCase(false);
                                actionSequenceContext.setTestConfig(excelCaseContext.getTestConfig());
                                actionSequenceContext.setSmokingTestConfigModel(SmokingTestJsonConfig.getInstance().getSmokingTestConfig());
                                if (mainModel.getAppInfo().isEnableUserLog()) {
                                    actionSequenceContext.setUserLogPath(mainModel.getAppInfo().getUserLogPath());
                                }
                                int currentRow = excelRow + 1;
                                //存数据库
                                String userColumnName = String.format("第%d行", currentRow);
                                //执行行测试
                                log.info("{}:开始执行动作序列->{}", currentRow, actionSequenceContext);
                                ActionSequenceCheckReporter actionSequenceCheckReporter = excelCaseTable.executeActionSequence(excelRow, userColumnName, actionSequenceContext);
                                log.info("{}:结束执行动作序列", currentRow);

                                log.info("{}:更新单行颜色", currentRow);
                                excelCaseTable.renderActionSequenceGrammarCheckResult(excelRow, null, actionSequenceCheckReporter, false);

                                //更新实际结果、测试结果、测试人员、测试时间
                                log.info("{}:更新单行测试结果", currentRow);
                                //SwingUtil.invokeLater(() -> { 后面会用到数据库数据进行写数据库 异步导致未更新数据前就执行了saveExcelCaseRowDataToDB
                                updateTableResultGUI(excelCaseTable, excelRow, actionSequenceCheckReporter);
                                //单行测试失败率
                                log.info("{}:更新单行失败率", currentRow);
                                updateRowFailRate(excelCaseTable, excelRow, testCycles, actionSequenceCheckReporter);
                                log.info("{}:保存单行到数据库", currentRow);
                                excelCaseTable.saveExcelCaseRowDataToDB(true, excelRow);
                                log.info("{}:打印单行测试结果", currentRow);
                                excelCaseTable.printActionSequenceReport(userColumnName, actionSequenceCheckReporter, false);
                                if (!actionSequenceCheckReporter.isExecuteOk()) {
                                    completeOk = false;
                                    pass = false;
                                }

//                              if (isPressureMode && !pass) {
//                                 updatePausedUiState();
//                               }
                                //已修复SSE消息，还是用SSE消息通知逻辑
                                if (isPause()) {
                                    synchronized (pauseLock) {
                                        try {
                                            pauseLock.wait();
                                        } catch (InterruptedException e) {
                                            return null;
                                        }
                                    }
                                    if (shouldStop.get()) {
                                        log.info("检测到测试停止标志位");
                                        break;
                                    }
                                } else {
                                    if (rowExecuteIntervalTime > 0) {
                                        try {
                                            log.info("等待:{}ms", rowExecuteIntervalTime);
                                            Thread.sleep(rowExecuteIntervalTime);
                                        } catch (InterruptedException e) {
                                            log.error(e.getMessage(), e);
                                        }
                                    }
                                }
                            }
                            if (pass) {
                                pastCases++;
                            }
                            // 在每行执行后更新 testResultMap
                            TestResult testResult = testResultMap.computeIfAbsent(excelCaseTable.getSheetName(), k -> {
                                TestResult tr = new TestResult();
                                tr.setIndex(testResultMap.size() + 1);
                                tr.setModuleName(excelCaseTable.getSheetName());
                                tr.setExecutedCases(0);
                                tr.setPassCases(0);
                                SmokingTestConfigModel smokingTestConfig = SmokingTestJsonConfig.getInstance().getSmokingTestConfig();
                                if (smokingTestConfig.getReportPassRate() != null) {
                                    tr.setPassRate(Integer.parseInt(smokingTestConfig.getReportPassRate()));
                                }
                                return tr;
                            });
                            // 更新已执行用例数和通过用例数
                            testResult.setExecutedCases(testResult.getExecutedCases() + 1);
                            if (pass) {
                                testResult.setPassCases(testResult.getPassCases() + 1);
                            }
                        }
                        SwingUtil.invokeLater(() -> excelCaseTable.clearSelection());
                    }
                }
                //完成
                actionSequenceStatus.setCompleteOk(completeOk);
                OperationTargetHolder.getActionSequenceKit().completeActionSequence(actionSequenceStatus);
                return null;
            }

            @Override
            protected void process(List<ExcelCaseTable.RowGroup> rows) {
                for (ExcelCaseTable.RowGroup row : rows) {
                    int actuallyRowIndex = row.getActuallyRowIndex();
                    int testRowIndex = row.getTestRowIndex();
                    excelCaseTable.setRowSelectionInterval(actuallyRowIndex, actuallyRowIndex);
                    excelCaseTable.progressBarValueSet(testRowIndex, excelCaseTable.getCheckedRows().size());
                    excelCaseTable.scrollToRow(row.actuallyRowIndex);
                }
            }


            @Override
            protected void done() {
                SwingUtil.invokeLater(excelCaseTestListener::endTest);
                if (excelCaseTable != null) {
                    excelCaseTable.stopProgressBar();
                }
                firstExecuteTest = true;
                if (!excelCaseContext.isSimulated()) {
                    //保存用例
                    new SwingWorker<Void, Void>() {
                        @Override
                        protected Void doInBackground() throws Exception {
                            excelCaseControlPanel.exportExcelCaseReport(EXPORT_REPORT_FILE.name());
                            //报告复制到网盘
                            sendReportToCloud(CaseConfigJson.getInstance().getExportExcelCaseFilePath());
                            //发送邮件报告和robot报告
                            sendMailAndRobotTestReport();
                            testResultMap.clear(); // 清空之前的测试结果
                            if (mainModel.getAppInfo().isSmokeTest() && "true".equalsIgnoreCase(System.getenv("FLYTEST_SKIP_LOGIN"))) {
                                //上传给python进程结果
                                uploadResult("{\"result\":\"success\"}");
                                SwingUtil.showNonModalDialog(null, "此次任务已结束，系统将在3分钟后退出！");
                                //等待3分钟后
                                log.info("cicd冒烟测试已完成，3分钟后将自动退出系统...");
                                // 使用javax.swing.Timer替代Thread.sleep()，避免线程暂停导致卡面冻住
                                javax.swing.Timer timer = new Timer(3 * 60 * 1000, event -> {
                                    clientView.shutdownApp();
                                    System.exit(0);
                                });
                                timer.setRepeats(false); // 设置为只执行一次
                                timer.start();
                            }
                            return null;
                        }
                    }.execute();
                }

            }

            @SneakyThrows
            private void sendMailAndRobotTestReport() {
                if (mainModel.getAppInfo().isEnableSequenceEmailSending() || mainModel.getAppInfo().isEnableSequenceRobotSending() || mainModel.getAppInfo().isEnableSequenceCloudDocSending()) {
                    SmokingTestConfigModel smokingTestConfig = SmokingTestJsonConfig.getInstance().getSmokingTestConfig();
                    TestResultReportDto reportTestDto = TestResultReportDto.getInstance();
                    reportTestDto.setQnxVersion(mainModel.getTestVersion().getQnxVersion());
                    reportTestDto.setSocVersion(mainModel.getTestVersion().getSocVersion());
                    reportTestDto.setMcuVersion(mainModel.getTestVersion().getMcuVersion());
                    reportTestDto.setVersion(mainModel.getTestVersion().getVersion());
                    reportTestDto.setStartTime(startTime);
                    reportTestDto.setEndTime(DateUtils.getNow());
                    reportTestDto.setLocalReportPath(CaseConfigJson.getInstance().getExportExcelCaseFilePath());
                    reportTestDto.setCloudReportPath(smokingTestConfig.getReportUrl());
                    reportTestDto.setFunctionMode(!mainModel.getAppInfo().isSmokeTest());
                    if (smokingTestConfig.getReportPassRate() != null) {
                        reportTestDto.setPassRate(Integer.parseInt(smokingTestConfig.getReportPassRate()));
                    }
                    if (reportTestDto.isFunctionMode()) {
                        reportTestDto.setFunctionTestResults(new ArrayList<>(testResultMap.values()));
                    } else {
                        reportTestDto.setSmokeTestResults(testResultMap.values().stream().findFirst().orElse(null));
                    }
                    if (mainModel.getAppInfo().isEnableSequenceRobotSending()) {
                        OperationTargetHolder.getActionSequenceKit().setRobotTestResult(reportTestDto);
                    }
                    if (mainModel.getAppInfo().isEnableSequenceEmailSending()) {
                        OperationTargetHolder.getActionSequenceKit().setMailTestResult(reportTestDto);
                    }
                    if (mainModel.getAppInfo().isEnableSequenceCloudDocSending()) {
                        OperationTargetHolder.getActionSequenceKit().setCloudDocTestResult(reportTestDto);
                    }
                }

            }
        };
        executeTestSwingWorker.execute();
    }


    private void createCloudPath(String startTimeFormat) {
        SmokingTestConfigModel smokingTestConfig = SmokingTestJsonConfig.getInstance().getSmokingTestConfig();
        String cloudBaseUrl = smokingTestConfig.getReportUrl();
        if (cloudBaseUrl != null && !cloudBaseUrl.isEmpty()) {
            String reportTimeFileDirUrl = cloudBaseUrl + "/" + startTimeFormat;
            smokingTestConfig.setReportTimeFileDirPath(reportTimeFileDirUrl);
            // 创建目标文件夹以时间命名文件夹
            Path targetDirectory = Paths.get(reportTimeFileDirUrl);
            if (!Files.exists(targetDirectory)) {
                try {
                    log.info("开始创建网盘路径: {}", targetDirectory.toString());
                    Files.createDirectories(targetDirectory);  // 创建多级目录
                    //再在targetDirectory路径下，在创建两个文件夹
                    Files.createDirectories(targetDirectory.resolve("模板图像"));
                    Files.createDirectories(targetDirectory.resolve("失败图像"));
                } catch (IOException e) {
                    log.error("创建网盘路径失败：{}", e.getMessage());
                }
                log.info("已创建网盘路径: {}", targetDirectory.toString());
            }
        }
    }

    /**
     * 上传测试结果
     *
     * @param resultJson 测试结果JSON
     * @throws Exception 异常
     */
    public static void uploadResult(String resultJson) throws Exception {
        URL url = new URL("http://localhost:5000/receive_test_result");
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        conn.setRequestMethod("POST");
        conn.setRequestProperty("Content-Type", "application/json; utf-8");
        conn.setDoOutput(true);

        try (OutputStream os = conn.getOutputStream()) {
            byte[] input = resultJson.getBytes("utf-8");
            os.write(input, 0, input.length);
        }

        int code = conn.getResponseCode();
        if (code == 200) {
            log.info("测试结果上传到cicd_tool成功！");
        } else {
            log.error("测试结果上传到cicd_tool失败，HTTP状态码：{}", code);
        }
    }

    /**
     * 上传测试报告
     *
     * @param localReportPath 本地报告路径
     */
    private void sendReportToCloud(String localReportPath) {
        //放上网盘
        SmokingTestConfigModel smokingTestConfig = SmokingTestJsonConfig.getInstance().getSmokingTestConfig();
        String reportUrl = convertToUNCPath(smokingTestConfig.getReportTimeFileDirPath());
        if (reportUrl != null && !reportUrl.isEmpty()) {
            try {
                copyFileToNetworkDrive(localReportPath, reportUrl);
            } catch (IOException e) {
                log.error("将报告文件复制到网盘路径失败", e);
            }
        }
    }

    /**
     * 根据映射规则将路径转换为 UNC 路径
     *
     * @param path 原始路径
     * @return 转换后的 UNC 路径
     */
    private String convertToUNCPath(String path) {

        if (path == null) {
            return null;
        }
        // 如果路径已经是 UNC 格式，直接返回
        if (path.startsWith("\\")) {
            return path;
        }
        // 检查路径前缀并转换为对应的 UNC 路径
        for (Map.Entry<String, String> entry : DRIVE_TO_UNC_MAP.entrySet()) {
            if (path.startsWith(entry.getKey())) {
                return path.replace(entry.getKey(), entry.getValue());
            }
        }
        // 如果没有匹配的驱动器，返回原始路径
        return path;
    }

    /**
     * 将文件复制到指定的网盘路径
     *
     * @param localFile 本地文件路径
     * @param reportUrl 目标网盘路径
     * @throws IOException 如果复制过程中出现错误
     */
    private void copyFileToNetworkDrive(String localFile, String reportUrl) throws IOException {
        Path sourcePath = Paths.get(localFile);
        Path targetPath = Paths.get(reportUrl, sourcePath.getFileName().toString());

        // 检查目标文件是否已存在，存在则覆盖
        Files.copy(sourcePath, targetPath, StandardCopyOption.REPLACE_EXISTING);
    }

    /**
     * 创建测试报告
     *
     * @param excelCaseContext excel用例上下文
     */
    private void createTestReport(ExcelCaseContext excelCaseContext, String startTimeFormat) {
        if (!excelCaseContext.isSmokingTest()) {
            //生成报告文件
            createCaseExportFile(startTimeFormat);
        } else {
            createSmokingTestCaseExportFile();
        }
    }

    /**
     * 获取选中的表格
     *
     * @param excelCaseContext excel用例上下文
     * @return excel表格集合
     */
    private List<ExcelCaseTable> tableCheckedRows(ExcelCaseContext excelCaseContext) {
        List<ExcelCaseTable> excelCaseTableList = excelCaseContext.getExcelCaseTableList();
        List<String> list = new ArrayList<>();
        for (ExcelCaseTable excelCaseTable : excelCaseTableList) {
            if (excelCaseTable.getCheckedRows().isEmpty()) {
                list.add(excelCaseTable.getSheetName());
            }
        }
        if (!list.isEmpty()) {
            JOptionPane.showMessageDialog(excelCaseRenderTabbedPane, String.format("表名[%s]没有勾选的用例", list),
                    "提示", JOptionPane.INFORMATION_MESSAGE);
            return null;
        }
        return excelCaseTableList;
    }

    /**
     * 上传测试用例执行信息
     *
     * @param excelRow                    excel行
     * @param actionSequenceCheckReporter 执行报告
     */
    private static void uploadExcelTestCaseExecuteInfo(Integer excelRow, ActionSequenceCheckReporter actionSequenceCheckReporter) {
        int testedTimes = 1;
        int testedFailTimes = 1;
        ActionSequenceContext actionSequenceContext1 = actionSequenceCheckReporter.getActionSequenceContext();
        if (actionSequenceContext1 != null) {
            ExcelCaseModel excelCaseModel = actionSequenceContext1.getExcelCaseModel();
            testedTimes = Integer.parseInt(excelCaseModel.getTestedTimes());
            testedFailTimes = testedTimes - Integer.parseInt(excelCaseModel.getTestedPassTimes());
        }
        TestCaseExecutionInfo testCaseExecutionInfo = new TestCaseExecutionInfo(actionSequenceCheckReporter.isExecuteOk() ? 2 : 3, DateUtils.getNow(),
                testedFailTimes, testedTimes, 0, excelRow + 1, 0, "");
        OperationTargetHolder.getTestSuiteKit().updateCaseToServer(testCaseExecutionInfo);
    }

    /**
     * 准备测试用例的执行环境，配置日志相关设置。
     *
     * @param actionSequenceStatus 包含需要准备执行的动作序列信息的状态对象
     */
    private void prepareExecution(ActionSequenceStatus actionSequenceStatus) {
        String folder = String.format("D:\\FlyTest\\data\\server\\projects\\%s\\database\\fileDB\\logs", mainModel.getAppInfo().getProject());
        String logFileDir = Paths.get(folder, "ExcelCaseLog").toString();
        String logFilePath = Paths.get(logFileDir, "ExcelCaseLog_" + DateUtils.getNowForFile()).toString();
        CaseConfigJson.getInstance().setLogFilePath(logFilePath);
        actionSequenceStatus.setLogPath(logFilePath);
        OperationTargetHolder.getActionSequenceKit().prepareExecuteActionSequence(actionSequenceStatus);
    }

    /**
     * 更新单行测试失败率
     */
    private void updateRowFailRate(ExcelCaseTable table, Integer excelRow, int testCycles, ActionSequenceCheckReporter actionSequenceCheckReporter) {
        try {
            int failRateColumnId = table.findTableColumnIndexByName(FAIL_RATE);
            if (failRateColumnId == -1)
                return;
            String failRate = String.valueOf(table.getModel().getValueAt(excelRow, failRateColumnId));

            int executionTimesColumnId = table.findTableColumnIndexByName(Execution_TIMES);
            if (executionTimesColumnId == -1)
                return;
            int executeTimes = Integer.parseInt(String.valueOf(table.getModel().getValueAt(excelRow, executionTimesColumnId)));
            if (failRate.isEmpty()) {
                failRate = String.format("%d/%d", 0, testCycles * executeTimes);
            }
            if (!actionSequenceCheckReporter.isExecuteOk()) {
                failRate = calculateFailureRate(failRate);
            }
            table.setValueAt(failRate, excelRow, failRateColumnId);
        } catch (Exception e) {
            log.error("{}用例表没有列名：{}", table.getSheetName(), FAIL_RATE);
        }

    }

    /**
     * 创建用例导出文件
     */
    public void createCaseExportFile(String startTime) {
        String templateFilePath = CaseConfigJson.getInstance().getTemplateExcelCaseFilePath();
        File file = new File(templateFilePath);
        String fileName = file.getName();
        String baseName = fileName.substring(0, fileName.lastIndexOf('.'));
        String suffix = fileName.substring(fileName.lastIndexOf("."));
        String exportReportPath = CaseReportFileConfig.getInstance().getFunctionalTestReportFile() + String.format("\\%s%s", baseName, "测试报告_") + startTime + suffix;
        CaseConfigJson.getInstance().setExportExcelCaseFilePath(exportReportPath);
        FileUtils.copyFile(CaseConfigJson.getInstance().getOriginalExcelCaseFilePath(), CaseConfigJson.getInstance().getExportExcelCaseFilePath());
    }

    /**
     * 创建冒烟测试用例报告文件。
     */
    public void createSmokingTestCaseExportFile() {
        String templateFilePath = CaseConfigJson.getInstance().getTemplateExcelCaseFilePath();
        File file = new File(templateFilePath);
        String fileName = file.getName();
        String baseName = fileName.substring(0, fileName.lastIndexOf('.'));
        String suffix = fileName.substring(fileName.lastIndexOf("."));
        String exportReportPath = CaseReportFileConfig.getInstance().getSmokingTestReportFile() + String.format("\\%s%s", baseName, "测试报告_") + reportSimpleDateFormat.format(new Date()) + suffix;
        CaseConfigJson.getInstance().setExportExcelCaseFilePath(exportReportPath);
        FileUtils.copyFile(CaseConfigJson.getInstance().getOriginalExcelCaseFilePath(), CaseConfigJson.getInstance().getExportExcelCaseFilePath());
    }

    /**
     * 更新用例配置，包含选中表及测试次数。
     */
    public void updateCaseConfig() {
        int testMode = excelCaseControlPanel.getEnableFailStopCheckBox().isSelected() ? 0 : 1;
        Object[] selectedSheetNamesArray = excelCaseControlPanel.getMultiComboBox().getSelectedValues();
        Map<String, Boolean> selectedSheetMap = CaseConfigJson.getInstance().getSelectedSheetMap();
        selectedSheetMap.entrySet().forEach(sheet -> sheet.setValue(false));

        Stream.of(selectedSheetNamesArray)
                .forEach(obj -> {
                    String sheetName = obj.toString();
                    if (selectedSheetMap.containsKey(sheetName)) {
                        selectedSheetMap.put(sheetName, true);
                    }
                }); // 更新Map对应的key的value值

        CaseConfigJson.getInstance().setTestMode(testMode);
        CaseConfigJson.getInstance().setTestTimes(testMode == 0 ? (int) excelCaseControlPanel.getTestCycleSpinner().getValue() : 1);
        CaseConfigJson.getInstance().setSelectedSheetMap((LinkedHashMap<String, Boolean>) selectedSheetMap);
//        CaseConfigJson.getInstance().setSelectedCaseNameList(selectedSheetNamesArray);
        CaseConfigJsonManager.syncExcelCaseConfigFile();
    }

    /**
     * 更新测试模式并同步配置文件。
     */
    public void updateTestModeConfig() {
        int testMode = excelCaseControlPanel.getEnableFailStopCheckBox().isSelected() ? 0 : 1;
        CaseConfigJson.getInstance().setTestMode(testMode);
        CaseConfigJson.getInstance().setTestTimes(testMode == 0 ? (int) excelCaseControlPanel.getTestCycleSpinner().getValue() : 1);
        CaseConfigJsonManager.syncExcelCaseConfigFile();
    }

    /**
     * 显示右侧面板。
     */
    public void showRightPaneView() {
        //打开右边面板和修改控制图标
        clientView.getRightToolbarPanelView().updateStepOrActionSideView(false);
    }

    /**
     * 清除行的失败次数统计。
     *
     * @param table 用例表
     */
    public void clearRowFailResult(ExcelCaseTable table) {
        try {
            int failRateColumnId = table.findTableColumnIndexByName(FAIL_RATE);
            if (failRateColumnId == -1)
                return;
            for (int row = 0; row < table.getRowCount(); row++) {
                table.setValueAt("", row, failRateColumnId);
            }
        } catch (Exception e) {
            log.error("{}用例表没有列名：{}", table.getSheetName(), FAIL_RATE);
        }

    }

    /**
     * 清除测试结果的相关数据列。
     *
     * @param table 用例表
     */
    public void clearTestResult(ExcelCaseTable table) {
        log.info("开始测试前，清除测试结果");
        try {
            List<String> clearColumns = new ArrayList<>();
            int testResultColumnId = table.findTableColumnIndexByName(ColumnNameConstants.getInstance().getTestResult());
            int actualResultColumnId = table.findTableColumnIndexByName(ColumnNameConstants.getInstance().getActualResult());
            int testerColumnId = table.findTableColumnIndexByName(ColumnNameConstants.getInstance().getTester());
            int testTimeColumnId = table.findTableColumnIndexByName(ColumnNameConstants.getInstance().getTestTime());
            TableModel model = table.getModel();
            for (int row = 0; row < table.getRowCount(); row++) {
                setDefaultValueIfValid(model, row, actualResultColumnId, "");
                setDefaultValueIfValid(model, row, testResultColumnId, "");
                setDefaultValueIfValid(model, row, testerColumnId, "");
                setDefaultValueIfValid(model, row, testTimeColumnId, "");
            }
            Map<Integer, String> columnIdToNameMap = new HashMap<>();
            columnIdToNameMap.put(actualResultColumnId, getColumnName(actualResultColumnId));
            columnIdToNameMap.put(testResultColumnId, getColumnName(testResultColumnId));
            columnIdToNameMap.put(testerColumnId, getColumnName(testerColumnId));
            columnIdToNameMap.put(testTimeColumnId, getColumnName(testTimeColumnId));
            for (Map.Entry<Integer, String> entry : columnIdToNameMap.entrySet()) {
                if (entry.getKey() != -1) {
                    clearColumns.add(entry.getValue());
                }
            }
            if (clearColumns.isEmpty()) return;
            Map<String, Object> params = new HashMap<>();
            params.put("sheetName", table.getSheetName());
            params.put("columns", clearColumns);
            OperationTargetHolder.getExcelKit().clearMapColumnData(params);  //界面上清空测试人员时间等数据，并更新数据库中
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    /**
     * 设置表格指定行的指定列的值，如果指定列不存在则不进行操作。
     *
     * @param model        表格模型
     * @param row          行号
     * @param columnId     列的标识符，表示要设置的列
     * @param defaultValue 默认值
     */
    private void setDefaultValueIfValid(TableModel model, int row, int columnId, String defaultValue) {
        if (columnId != -1) {
            try {
                model.setValueAt(defaultValue, row, columnId);
            } catch (Exception e) {
                // 处理异常，例如记录日志或显示错误信息
                log.error("Error setting value at row {}, column {}: {}", row, columnId, e.getMessage());
            }
        }
    }

    /**
     * 根据列标识符生成对应的列名称
     *
     * @param columnId 列的数值标识符，表示需要生成名称的列序号
     * @return 格式为"columnN"的字符串，其中N为输入的columnId参数值
     */
    private static String getColumnName(int columnId) {
        return String.format("column%d", columnId);
    }


    /**
     * 更新用例表格中的测试结果。
     *
     * @param table                       用例表
     * @param testViewRow                 用例所在视图行
     * @param actionSequenceCheckReporter 检查结果报告
     */
    public void updateTableResultGUI(ExcelCaseTable table, int testViewRow, ActionSequenceCheckReporter actionSequenceCheckReporter) {
        try {
            String testResult;
            if (!actionSequenceCheckReporter.isCheckOk()) {
                testResult = ExcelConstants.FAIL_RESULT;
            } else {
                testResult = actionSequenceCheckReporter.isExecuteOk() ? ExcelConstants.PASS_RESULT : ExcelConstants.FAIL_RESULT;
            }
            int testResultColumnId = table.findTableColumnIndexByName(ColumnNameConstants.getInstance().getTestResult());
            int actualResultColumnId = table.findTableColumnIndexByName(ColumnNameConstants.getInstance().getActualResult());
            int testerColumnId = table.findTableColumnIndexByName(ColumnNameConstants.getInstance().getTester());
            int testTimeColumnId = table.findTableColumnIndexByName(ColumnNameConstants.getInstance().getTestTime());
            int testLogColumnId = table.findTableColumnIndexByName(ColumnNameConstants.getInstance().getTestLog());
            int testCaseIDColumnId = table.findTableColumnIndexByName(ColumnNameConstants.getInstance().getTestCaseID());
            String actualResult = "";
            if (actionSequenceCheckReporter.getActualResult() != null) {
                actualResult = actionSequenceCheckReporter.getActualResult().toString();
            }
            if (testResultColumnId != -1) {
                table.setValueAt(testResult, testViewRow, testResultColumnId);
            }
            if (actualResultColumnId != -1) {
                table.setValueAt(actualResult, testViewRow, actualResultColumnId);
            }
            if (testerColumnId != -1) {
                table.setValueAt(mainModel.getAppInfo().getUserName(), testViewRow, testerColumnId);
            }
            if (testTimeColumnId != -1) {
                table.setValueAt(sdf.format(new Date()), testViewRow, testTimeColumnId);
            }
            if (SaveLogDialog.getInstance(mainModel).isSaveTestLog()) {
                if (testLogColumnId != -1 && testCaseIDColumnId != -1) {
                    table.setValueAt(table.getModel().getValueAt(testViewRow, testCaseIDColumnId), testViewRow, testLogColumnId);
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    @Override
    public void excelCaseExecutionTesting(ExcelCaseExecutionNotification notification) {
        TestStepTable testStepTable = clientView.getTestStepView().getTestStepTable();
        mainModel.getTestStepModel().renderTestStepRowColorByUuid(notification.getUuid());
        testStepTable.addExecutionResult(notification.getRow(), notification.getExecuteResults());
    }

    @Override
    public void excelCaseExecutionPausing(ExcelCaseExecutionNotification notification) {

    }

    @Override
    public void excelCaseExecutionAllCompleted(ExcelCaseExecutionNotification notification) {

    }

    @Override
    public void excelCaseExecutionException(ExcelCaseExecutionNotification notification) {

    }

    @Override
    public void excelCaseExecutionCheckResult(ExcelCaseExecutionNotification notification) {
        updateActualResult(notification.getExtraMessage());
    }

    public void updateDIDToExcelCaseTable(String selectedProtocol, Map<Integer, Map<String, String>> didMap) {
        ExcelCaseTable table = excelCaseRenderTabbedPane.getSelectedExcelCaseTable();
        int actionSequencesColumnId = table.findTableColumnIndexByName(ColumnNameConstants.getInstance().getOperationStepSequences());
        int expectedResultSequencesColumnId = table.findTableColumnIndexByName(ColumnNameConstants.getInstance().getExpectedResultSequences());
        for (int row = 0; row < 6; row++) {
            Map<String, String> didMapInfo = didMap.get(row);
            for (Map.Entry<String, String> entry : didMapInfo.entrySet()) {
                String id = entry.getKey();
                String value = entry.getValue();
                String action = "";
                int updateDidRow = row;
                if (selectedProtocol.equalsIgnoreCase("UDP")) {
                    action = String.format("1.%s-setDID-%s-%s-wait-1s-check-1", selectedProtocol, id, value.replace("-", "_"));
                } else {
                    action = String.format("1.wait-1s-check-1", selectedProtocol, id, value.replace("-", "_"));
                    updateDidRow = row + 1;
                }
                String expectedResult = String.format("1.%s-getDID-%s-%s", selectedProtocol, id, value.replace("-", "_"));
                table.setValueAt(action, updateDidRow, actionSequencesColumnId);
                table.setValueAt(expectedResult, updateDidRow, expectedResultSequencesColumnId);
            }
        }
//        excelCaseControlPanel.getExcelCaseLoader().updateExcelCaseToOriginalFile(); //让用户自己点击保存用例
    }

    public void insertInitScripts(InitScriptModel initScriptModel, InsertTestCaseListener listener) {
        try {
            ExcelCaseTable table = excelCaseRenderTabbedPane.getSelectedExcelCaseTable();
            boolean selectedAll = initScriptModel.isSelectedAll();
            String initScript = initScriptModel.getInitScripts();
            if (selectedAll || initScriptModel != null) {
                String preconditionSequences = ColumnNameConstants.getInstance().getPreconditionSequences();
                int preconditionIndex = table.findTableColumnIndexByName(preconditionSequences);
                // 校验列索引有效性
                if (preconditionIndex < 0 || preconditionIndex >= table.getColumnCount()) {
                    throw new IllegalArgumentException("Invalid column index for: " + preconditionSequences);
                }
                // 处理空值初始化脚本
                String safeInitScript = initScript != null ? initScript : "";
                // 确定行范围
                int startRow = selectedAll ? 0 : Math.max(initScriptModel.getStartRow(), 0);
                int endRow = selectedAll ? (table.getRowCount() - 1)
                        : Math.min(initScriptModel.getEndRow(), table.getRowCount() - 1);
                // 统一处理行数据
                for (int row = startRow; row <= endRow; row++) {
                    String preconditionValue = (String) table.getModel().getValueAt(row, preconditionIndex);
                    String safePreconditionValue = preconditionValue != null ? preconditionValue : "";
                    String newPreconditionValue = String.join("\n", safeInitScript, safePreconditionValue);
                    table.setValueAt(newPreconditionValue, row, preconditionIndex);
                    table.updateRowHeight(row);
                    table.updateFrozenHeight(row);
                }
            }
            listener.operateResult(true);
        } catch (Exception e) {
            log.error("Error occurred while inserting init scripts: ", e);
            listener.operateResult(false);
        }
    }
}