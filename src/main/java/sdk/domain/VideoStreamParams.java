package sdk.domain;

/**
 * 视频流参数
 */
public class VideoStreamParams {
    
    private int width;
    private int height;
    private int bitRate;
    
    public VideoStreamParams() {
    }
    
    public VideoStreamParams(int width, int height, int bitRate) {
        this.width = width;
        this.height = height;
        this.bitRate = bitRate;
    }
    
    public int getWidth() {
        return width;
    }
    
    public void setWidth(int width) {
        this.width = width;
    }
    
    public int getHeight() {
        return height;
    }
    
    public void setHeight(int height) {
        this.height = height;
    }
    
    public int getBitRate() {
        return bitRate;
    }
    
    public void setBitRate(int bitRate) {
        this.bitRate = bitRate;
    }
    
    @Override
    public String toString() {
        return String.format("VideoStreamParams{width=%d, height=%d, bitRate=%d}", width, height, bitRate);
    }
}
