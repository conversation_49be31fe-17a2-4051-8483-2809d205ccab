package sdk.base.operation;

import com.alibaba.fastjson2.TypeReference;
import common.constant.AppConstants;
import common.utils.DateUtils;
import common.utils.NetworkUtils;
import sdk.base.BaseHttpClient;
import sdk.base.HttpContext;
import sdk.base.JsonResponse;
import sdk.constants.PolyTestConstants;
import sdk.constants.UrlConstants;
import sdk.entity.OperationTargetHolder;
import sdk.entity.interfaces.ITestSuite;
import ui.model.MainModel;

import java.util.ArrayList;

import static sdk.constants.PolyTestConstants.getValueByStatus;

public class TestSuiteKit extends BaseHttpClient implements ITestSuite {
    @Override
    public JsonResponse<String> startTestSuite(TestSuiteInfo testSuiteInfo) {
        HttpContext httpContext = new HttpContext();
        httpContext.setTimeout(30);
        return defaultPostJsonResponse(UrlConstants.TestSuiteUrls.UPLOAD_TEST_SUITE_INFO,
                testSuiteInfo,
                new TypeReference<JsonResponse<String>>() {
                },
                httpContext);
    }

    @Override
    public JsonResponse<String> updateCaseToServer(TestCaseExecutionInfo testCaseExecutionInfo) {
        return defaultPostJsonResponse(UrlConstants.TestSuiteUrls.UPLOAD_TEST_CASE_EXECUTE_INFO,
                testCaseExecutionInfo,
                new TypeReference<JsonResponse<String>>() {
                });
    }


    @Override
    public JsonResponse<String> startManuallyTest(MainModel mainModel) {
        TestSuiteInfo testSuiteInfo = TestSuiteInfo.builder()
                .softwareVersion("")
                .hardwareVersion("")
                .testboardVersion("")
                .pcName(NetworkUtils.getComputerInfo().getComputerName() + "_ManuallyPanel")
                .projectId(mainModel.getAppInfo().getProjectId())
                .startTime(DateUtils.getNow())
                .testCases(new ArrayList<>())
                .testType(getValueByStatus(PolyTestConstants.TestTypeConstants.CICD_TEST))
                .testsuiteName("二次面板_" + DateUtils.getNow())
                .toolId(AppConstants.PLATFORM_CODE)
                .userId(mainModel.getAppInfo().getUserId())
                .build();
        OperationTargetHolder.getTestSuiteKit().startTestSuite(testSuiteInfo);
        return defaultGetJsonResponse(UrlConstants.TestSuiteUrls.UPLOAD_MANUALLY_TEST_INFO,
                new TypeReference<JsonResponse<String>>() {
                });
    }
}
